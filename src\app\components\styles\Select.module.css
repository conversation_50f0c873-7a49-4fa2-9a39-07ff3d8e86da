.selectWrapper {
  display: flex;
  gap: 6px;
  flex-direction: column;
}

.select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: flex;
  flex: 1;
  min-width: 100%;
  padding: 10px 14px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: white;
  font-family: Inter;
  color: #000;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  /* Using a more basic arrow approach */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333333'%3E%3Cpath d='M0 3 L6 9 L12 3 Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 14px center;
  background-size: 12px;
  padding-right: 42px;
  position: relative;
  z-index: 1;
}

/* Add a fallback arrow using pseudo-element in case the SVG doesn't work */
.select::after {
  content: '';
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  pointer-events: none;
  z-index: 2;
  display: none;
  /* Only show if needed */
}

.select:focus {
  border-color: #c19a6b;
  outline: none;
}

.error {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  border-color: #d92d20;
  outline: none;
}

.errorText {
  /* Text sm/Regular */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */

  color: #d92d20;
}
