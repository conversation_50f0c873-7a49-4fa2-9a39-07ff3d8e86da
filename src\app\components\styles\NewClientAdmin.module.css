.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0px;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Espaciado entre el icono y el texto */
  align-self: flex-start;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  background-color: #fff;
  border: none;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  color: #a67c52;
}

.icon {
  color: #000; /* Color del icono */
  transition: color 0.3s ease;
}

.backButton:hover .icon {
  color: #a67c52; /* Cambio de color del icono al pasar el mouse */
}

.text {
  color: #475467;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  align-self: stretch;
}

.tabContainer {
  display: flex;
  gap: 8px;

  justify-content: center;
  align-items: center;
  align-self: stretch;
  /* border-bottom: 1px solid #EAECF0; */
}

.tabContent {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.tab,
.activeTab {
  display: flex;
  width: 240px;
  height: 44px;
  padding: 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  border: none;
  outline: none;
}

.activeTab {
  border-bottom: 2px solid #000;
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.tab {
  color: #6f7280;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  border-bottom: 2px solid #eaecf0;
}

.formContainer {
	display: flex;
	flex-direction: column;
  gap: 1rem 2rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;
}

.inputGroup label {
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.inputGroup input,
.inputGroup select {
  width: 100%;
  display: flex;
  padding: 10px 14px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: white;
  font-family: Inter;
  color: #000;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

.inputGroup input:focus,
.inputGroup select:focus {
  border-color: #c19a6b;
  outline: none;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  padding: 24px 0px 0px;
}

.passwordWrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.eyeButton {
  background: none;
  border: none;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 16px;
  height: 16px;
  color: #000000;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eyeButton:focus {
  outline: none;
}

.error {
  color: #d92d20;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
}

.success {
  color: #4B9EF2;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
}

.inputGroup input.error,
.inputGroup select.error {
  border-color: #d92d20;
}

.inputGroup input.error:focus,
.inputGroup select.error:focus {
  border-color: #d92d20;
  outline: none;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.inputWrapper .prefix {
  position: absolute;
  left: 10px;
  font-size: 14px;
  color: #555;
}

.inputWrapper .suffix {
  position: absolute;
  right: 10px;
  font-size: 14px;
  color: #555;
}

.inputWrapper input {
  padding-left: 24px; /* espacio para el símbolo $ */
  padding-right: 24px; /* espacio para el símbolo % */
}

@media screen and (min-width: 650px) {
	.container {
		padding: 1rem;
	}

  .formContainer {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
  }
}
