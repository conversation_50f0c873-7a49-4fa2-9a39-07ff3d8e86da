'use client'
import styles from '../../styles/NewClientAdmin.module.css'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import InformationForm from '../components/InformationForm'
import FileUpload from '../components/FileUpload'
import ContactForm from '../components/ContactForm'
import { FileInput, ClientAdminProps } from '@/types/types'
import useFormValidation, { FormData as ClientFormData } from '@/hooks/useFormValidationClient'
import UploadDocumentSuccessModal from '../../modals/alertModals/UploadDocumentSuccessModal'
import CommissionForm from '../components/CommissionForm'
import ModalRegisteredRFC from '../../modals/alertModals/ModalRegisteredRFC'
import MultiCompanySelector from '../components/MultiCompanySelector'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { toast } from 'react-toastify'
import LoaderFull from '../../loader/LoaderFull'
import { StepTabs } from '../../StepTabs'

type ClientProps = ClientAdminProps & {
  setMembershipNumber: (number: string) => void // Callback para establecer el número de membresía
}

const informationFields: (keyof ClientFormData)[] = [
  'companyName',
  'alias',
  'rfc',
  'registrationDate',
  'responsibleName',
  'assignedCards',
  'state',
  'city',
]

const contactFields: (keyof ClientFormData)[] = [
  'phone',
  'email',
  'street',
  'zipCode',
  'neighborhood',
  'passwordClient',
  'confirmPassword',
]

const commissionFields: (keyof ClientFormData)[] = [
  'commissionPercentage',
  'commissionAmount',
  'commissionCardFunding',
  'commissionAmbassador',
]

export const STEPS = [
  { key: 'information', label: 'Información' },
  { key: 'contact', label: 'Contacto' },
  { key: 'commission', label: 'Comisión' },
] as const

type Step = (typeof STEPS)[number]['key']

const NewClientAdmin: React.FC<ClientProps> = ({ onOpenModal, setMembershipNumber }) => {
  const router = useRouter()
  const user = useAuthStore(state => state.user)
  const [openSuccessModal, setOpenSuccessModal] = useState(false)
  const [openRFCModal, setOpenRFCModal] = useState(false) // Estado del modal RFC
  const [savingInfoLoading, setSavingInfoLoading] = useState(false)
  const [isSucursal, setIsSucursal] = useState(false)
  const [currentStep, setCurrentStep] = useState<Step>(STEPS[0]['key'])
  const [formatFiles, setFormatFiles] = useState<
    { key: keyof ClientFormData['files']; file: string; type: string }[]
  >([])
  const stepIndex = STEPS.findIndex(step => step.key === currentStep)
  const { createAdmin, fetchAdminByRFC } = useAdminStore()
  const { formData, errors, setFormData, validate, handleInputChange, handleFileChange } =
    useFormValidation({
      companyName: '',
      alias: '',
      rfc: '',
      responsibleName: '',
      state: '',
      city: '',
      registrationDate: '',
      assignedCards: '',
      phone: '',
      email: '',
      street: '',
      zipCode: '',
      exteriorNumber: '',
      neighborhood: '',
      passwordClient: '',
      confirmPassword: '',
      files: {
        act: null,
        constancy: null,
        file: null,
        other: null,
      },
      commissionPercentage: '',
      commissionAmount: '',
      commissionCardFunding: '',
      commissionAmbassador: '',
      extraCompanies: [],
    })

  const fileInputs: FileInput[] = [
    { key: 'act', label: 'Cargar acta constitutiva' },
    { key: 'constancy', label: 'Cargar constancia' },
    { key: 'file', label: 'Cargar archivo' },
    { key: 'other', label: 'Cargar archivo' },
  ]

  const handleBack = () => {
    if (stepIndex > 0) {
      setCurrentStep(STEPS[stepIndex - 1].key)
    } else {
      router.push('/clientes')
    }
  }

  const handleNext = async () => {
    if (currentStep === 'information') {
      if (!validate([...informationFields, 'files'])) return
      // Verificar si el RFC ya está registrado
      try {
        // Cargar admins del backend con RFC incluido
        const baseRfc = formData.rfc.trim().toUpperCase()
        const rfcAlreadyExists = await fetchAdminByRFC(baseRfc)
        // Revisar si ya existe uno con ese RFC
        if (rfcAlreadyExists) {
          setIsSucursal(true)
          setOpenRFCModal(true)
        }
      } catch (err) {
        console.error('Error al validar RFC:', err)
        toast.error('No se pudo verificar el RFC en el sistema.', {
          position: 'top-right',
          autoClose: 3000,
          pauseOnHover: true,
          draggable: true,
        })
        return
      }
      setCurrentStep('contact')
    } else if (currentStep === 'contact') {
      if (!validate([...contactFields, 'extraCompanies'])) return
      setCurrentStep('commission')
    } else {
      handleSubmit()
    }
  }

  const handleSubmit = async () => {
    if (!validate([...informationFields, 'files'])) return
    setSavingInfoLoading(true)
    try {
      let filesPayload
      if (formatFiles) {
        filesPayload = formatFiles.map(({ file, type }) => ({ file, type }))
      }

      const response = await createAdmin({
        company_name: formData.companyName,
        alias: formData.alias,
        rfc: formData.rfc,
        is_sucursal: isSucursal,
        num_asigned_cards: parseInt(formData.assignedCards),
        address: {
          state: formData.state,
          city: formData.city,
          street: formData.street,
          zip_code: formData.zipCode,
          num_ext: formData.exteriorNumber,
          colonia: formData.neighborhood,
        },
        user: {
          name: formData.responsibleName,
          email: formData.email,
          phone: parseInt(formData.phone),
          password: formData.passwordClient,
          created_by: user?.id,
        },
        spei_in: parseFloat(formData.commissionPercentage || '0'),
        spei_out: parseFloat(formData.commissionAmount || '0'),
        ambassador: formData.commissionAmbassador,
        target_refound: parseFloat(formData.commissionCardFunding || '0'),
        enterprises: formData.extraCompanies.map(company => ({
          adminId: company,
        })),
        files: filesPayload,
      })
      if (response?.membership_number) {
        setMembershipNumber(response.membership_number.toString())
        onOpenModal()
      }
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : 'Ocurrió un error al crear el cliente.'
      toast.error(message)
    } finally {
      setSavingInfoLoading(false)
    }
  }

  const validationFieldsMap: Record<Step, (keyof ClientFormData)[]> = {
    information: informationFields,
    contact: contactFields,
    commission: commissionFields,
  }

  const isFormIncomplete = validationFieldsMap[currentStep].some(field => {
    const value = formData[field]
    return typeof value === 'string' && value.trim() === ''
  })

  return (
    <>
      <StepTabs
        steps={STEPS.map(s => s.label)}
        activeStep={stepIndex}
        onBack={handleBack}
        stepTitles={[
          'Por favor proporciona los siguientes datos',
          'Por favor completa la información de contacto',
          'Por favor proporciona la información de comisiones',
        ]}
        onStepChange={i => setCurrentStep(STEPS[i].key)}
        buttonText={currentStep === STEPS[STEPS.length - 1].key ? 'Guardar' : 'Siguiente'}
        disableNextButton={isFormIncomplete || savingInfoLoading}
        onNext={handleNext}
      >
        <StepTabs.Panel index={0}>
          <InformationForm formData={formData} onChange={handleInputChange} errors={errors} />
          <p className={styles.text}>Agregar documentación del cliente</p>
          <FileUpload
            fileInputs={fileInputs}
            files={formData.files}
            setFiles={handleFileChange}
            errors={errors}
            onSuccess={() => setOpenSuccessModal(true)}
            setFormatFiles={setFormatFiles} // Callback para establecer los archivos formateados
          />
        </StepTabs.Panel>
        <StepTabs.Panel index={1}>
          <ContactForm formData={formData} onChange={handleInputChange} errors={errors} />
          <MultiCompanySelector
            value={formData.extraCompanies || []}
            enabled={formData.extraCompanies.length > 0}
            onChange={companies => setFormData(prev => ({ ...prev, extraCompanies: companies }))}
            onToggle={enabled =>
              setFormData(prev => ({
                ...prev,
                extraCompanies: enabled ? [''] : [], // Si se activa, inicia con un array vacío
              }))
            }
            errors={errors}
          />
        </StepTabs.Panel>
        <StepTabs.Panel index={2}>
          <CommissionForm formData={formData} onChange={handleInputChange} errors={errors} />
        </StepTabs.Panel>
      </StepTabs>
      <ModalRegisteredRFC
        open={openRFCModal}
        onClose={() => setOpenRFCModal(false)}
        companyName={formData.companyName}
        alias={formData.alias}
      />
      <UploadDocumentSuccessModal
        open={openSuccessModal}
        onClose={() => setOpenSuccessModal(false)}
      />
      {savingInfoLoading && <LoaderFull />}
    </>
  )
}

export default NewClientAdmin
