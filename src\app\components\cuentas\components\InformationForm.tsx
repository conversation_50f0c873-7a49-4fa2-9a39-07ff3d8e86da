import React from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { Errors, FormData } from '@/hooks/useFormValidationAccount'
import { handleNumberOnlyInput, handleTextOnlyInput } from '@/utils/inputRestrictions'
import Select from '../../Select'
import { AdminBasic } from '@/types/admin/types'

type InformationFormProps = {
  formData: FormData
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  errors: Errors
  companies: Partial<AdminBasic>[]
  onSetCompanyId: (id: string) => void
}

const InformationForm = ({
  formData,
  onChange,
  errors,
  companies,
  onSetCompanyId,
}: InformationFormProps) => {
  // Función para manejar el cambio de empresa y actualizar el número de afiliación
  const handleCompanyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedCompanyName = e.target.value
    const selectedCompany = companies.find(company => company.companyName === selectedCompanyName)

    if (!selectedCompany) return // Salir de la función si no se encuentra la empresa seleccionada

    // Crear un evento sintético para el número de afiliación
    const affiliationEvent = {
      target: {
        name: 'affiliationNumber',
        value: String(selectedCompany.membershipNumber || ''),
      },
    } as React.ChangeEvent<HTMLInputElement>

    // Llamar a onChange con el evento original de la empresa
    onChange(e)

    // Llamar a onChange con el evento sintético para el número de afiliación
    onChange(affiliationEvent)
    // Llamar a onSetCompanyId con el ID de la empresa seleccionada

    if (!selectedCompany.id) return // Salir de la función si no se encuentra el ID de la empresa seleccionada
    onSetCompanyId(selectedCompany.id)
  }

  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Nombre completo</label>
        <input
          type="text"
          name="name"
          placeholder="Ej. Juan Pérez"
          value={formData.name}
          onChange={onChange}
          onKeyDown={handleTextOnlyInput}
          minLength={3}
          maxLength={50}
          className={errors.name ? styles.error : ''}
        />
        {errors.name && <span className={styles.error}>{errors.name}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Correo electrónico</label>
        <input
          type="email"
          name="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={onChange}
          className={errors.email ? styles.error : ''}
        />
        {errors.email && <span className={styles.error}>{errors.email}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Teléfono</label>
        <input
          type="tel"
          name="phone"
          placeholder="+52 55 1234 5678"
          value={formData.phone}
          onChange={onChange}
          onKeyDown={handleNumberOnlyInput}
          className={errors.phone ? styles.error : ''}
          maxLength={10}
        />
        {errors.phone && <span className={styles.error}>{errors.phone}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Fecha de alta</label>
        <input
          type="date"
          name="registrationDate"
          value={formData.registrationDate}
          readOnly
          className={errors.registrationDate ? styles.error : ''}
        />
        {errors.registrationDate && <span className={styles.error}>{errors.registrationDate}</span>}
      </div>
      <div className={styles.selectWrapper}>
        <Select
          label="Nombre empresa"
          name="companyName"
          value={formData.companyName}
          onChange={handleCompanyChange}
          onBlur={() => {}}
          isError={!!errors.companyName}
          errorText={errors.companyName}
        >
          {companies.map(company => (
            <option key={company.id} value={company.companyName}>
              {company.companyName}
            </option>
          ))}
        </Select>
      </div>
      <div className={styles.inputGroup}>
        <label>Número de afiliación</label>
        <input
          type="text"
          name="affiliationNumber"
          minLength={6}
          maxLength={10}
          value={formData.affiliationNumber}
          readOnly
          style={{ cursor: 'not-allowed' }}
        />
      </div>
    </div>
  )
}

export default InformationForm
