'use client'
import { useRouter } from 'next/navigation'
import { Suspense, useEffect, useLayoutEffect, useState } from 'react'
import EditAccount from './EditAccount'
import EditedAcountSuccess from '../../modals/informationModals/EditedAcountSuccess'
import Header from '../../Header'
import { useAccountStore } from '@/store/account/useAccountStore'
import LoaderFull from '../../loader/LoaderFull'
import { useAdminStore } from '@/store/admin/useAdminStore'

const AccountDetailsPage = () => {
  return (
    <Suspense fallback={<p>Cargando...</p>}>
      <Content />
    </Suspense>
  )
}

const Content = () => {
  const router = useRouter()
  const { fetchAccountById, isLoading: loading, accountSelected, clearSelected, accountSelected: account } = useAccountStore()
  const { fetchBasicAdmins, basicAdmins: companies, loading: loadingCompanies } = useAdminStore()

  const [modalOpen, setModalOpen] = useState(false) // ✅ Estado del modal

  // buscar la cuenta por su id
  useLayoutEffect(() => {
    clearSelected()
    if (account?.id) {
      fetchAccountById(account.id)
    }
  }, [account, clearSelected, fetchAccountById])

  useEffect(() => {
    fetchBasicAdmins()
  }, [fetchBasicAdmins])

  if (!accountSelected && !loading) {
    return <p>No se encontró la cuenta</p>
  }

  if (!accountSelected) {
    return null
  }

  return (
    <>
      <Header title={`Editar ${accountSelected.admin.companyName}`} />
      <EditAccount
        account={accountSelected}
        companies={companies}
        onOpenModal={() => setModalOpen(true)} // ✅ Pasar función
      />
      <EditedAcountSuccess open={modalOpen} onClose={() => router.push('/cuentas')} />
      {(loading || loadingCompanies) && <LoaderFull />}
    </>
  )
}

export default AccountDetailsPage
