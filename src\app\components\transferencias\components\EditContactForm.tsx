import React from 'react'
import GoBackButton from '@/app/components/GoBackButton'
import Button from '@/app/components/Button'
import formStyles from '../../../components/styles/NewClientAdmin.module.css'
import styles from '../../../components/styles/Transaction.module.css'
import { useRouter } from 'next/navigation'
import Input from '@/app/components/Input'
import { EditContactFormData } from '@/hooks/useFormValidationEditContactTransaction'
import { handleNumberOnlyInput } from '@/utils/inputRestrictions'

type Props = {
  formData: EditContactFormData
  errors: { [key: string]: string }
  touched: { [key: string]: boolean }
  isFormValid: boolean
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  handleBlur: (name: keyof EditContactFormData) => void
  onPressBtnEditContact: () => void
}

const EditContactForm: React.FC<Props> = ({
  formData,
  errors,
  touched,
  isFormValid,
  handleInputChange,
  handleBlur,
  onPressBtnEditContact,
}) => {
  const router = useRouter()
  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <h1 className={styles.title}>Transferencias [Arturo Diaz]</h1>
      <div style={{ marginTop: 34 }}>
        <GoBackButton onPress={() => router.back()} />
      </div>
      <p className={styles.subtitle}>
        Edita a tu contacto, cambia lo que requieras de este contacto
      </p>
      <div className={styles.wrapper} style={{ marginBottom: 24 }}>
        <div className={styles.inputGroup}>
          <Input
            label="Cuenta destino *"
            name="cuentaDestino"
            type="number"
            placeholder="Escribe aquí"
            onKeyDown={handleNumberOnlyInput}
            value={formData.cuentaDestino}
            onChange={handleInputChange}
            onBlur={() => handleBlur('cuentaDestino')}
            isError={(touched.cuentaDestino && !!errors.cuentaDestino) || false}
            errorText={errors.cuentaDestino}
          />
        </div>
        <div className={styles.formContainer}>
          <div className={styles.inputGroup}>
            <Input
              label="Nombre del beneficiario *"
              name="nombreDelBeneficiario"
              type="text"
              placeholder="Escribe aquí"
              value={formData.nombreDelBeneficiario}
              onChange={handleInputChange}
              onBlur={() => handleBlur('nombreDelBeneficiario')}
              isError={(touched.nombreDelBeneficiario && !!errors.nombreDelBeneficiario) || false}
              errorText={errors.nombreDelBeneficiario}
            />
          </div>
          <div className={styles.inputGroup}>
            <Input
              label="RFC beneficiario"
              type="text"
              name="rfcBeneficiario"
              placeholder="RFC"
              style={{ textTransform: 'uppercase' }}
              value={formData.rfcBeneficiario}
              onChange={handleInputChange}
              onBlur={() => handleBlur('rfcBeneficiario')}
              isError={(touched.rfcBeneficiario && !!errors.rfcBeneficiario) || false}
              errorText={errors.rfcBeneficiario}
              maxLength={13}
            />
          </div>
        </div>
        <div className={formStyles.inputGroup}>
          <Input
            label="Correo electrónico del beneficiario"
            type="text"
            name="correo"
            placeholder="Agregar correo"
            value={formData.correo}
            onChange={handleInputChange}
            onBlur={() => handleBlur('correo')}
            isError={(touched.correo && !!errors.correo) || false}
            errorText={errors.correo}
          />
        </div>
        <div className={styles.inputGroup}>
          <Input
            label="Escribe el nombre del alias del contacto que deseas guardar"
            name="aliasName"
            placeholder="Escribe aquí"
            type="text"
            value={formData.aliasName}
            onChange={handleInputChange}
            onBlur={() => handleBlur('aliasName')}
            isError={(touched.aliasName && !!errors.aliasName) || false}
            errorText={errors.aliasName}
          />
        </div>
      </div>
      <Button
        text="Editar contacto"
        onClick={onPressBtnEditContact}
        disabled={!isFormValid}
        fullWidth
      />
    </div>
  )
}

export default EditContactForm
