import { hasAccess, RoleName, ROLES } from '@/constants/roles'

export const canAccessNewTransfer = (roleName: RoleName | null): boolean => {
  if (!roleName) return false

  const allowed: RoleName[] = [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
  ]

  return allowed.includes(roleName)
}

export const canSubmitTransfer = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Ver transferencias realizadas */
export const canViewTransfers = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
  ])

/* Descargar comprobantes */
export const canDownloadReceipts = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Transferencia a un contacto */
export const canTransferToContact = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Transferencias masivas */
export const canMassTransfer = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

export const canCreateUser = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN])

export const showActionsInClientTable = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA])

// Envio adminId para cuentas de rol Cliente
export const shouldSendAdminId = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
    ROLES.CLIENTE_LECTOR,
  ])
