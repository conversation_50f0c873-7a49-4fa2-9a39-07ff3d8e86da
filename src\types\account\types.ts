export type AccountData = {
  name: string
  email: string
  phone: number
  adminId: string | null
  // companyName: string
}

export type AccountResponse = {
  id: string
  name: string
  email: string
  adminAlias: string
  clabe: string
  amount: number
  speiIn: boolean | null
  speiOut: boolean | null
  enabled: boolean
  isDeleteable: boolean
}

export type AccountListParams = unknown

export type AccountDetails = {
  name: string
  email: string
  accountNumberConvenia: string
  accountNumberTransfer: string
  accountNumberConveniaComplete: string
  available_resource?: number
  convenia_account?: string
}

export type AccountDetail = {
  id: string
  name: string
  email: string
  phone: string
  admin: {
    id: string
    companyName: string
    alias: string
    rfc: string
    numAsignedCards: number
    groupId: number
    createdAt: string
    membershipNumber: number
  }
}

export type GetCardResponse = {
  card: {
    id: string
    card_type: 'PHYSICAL' | 'VIRTUAL' // Enum para tipos de tarjeta
    format_expiration_date: string // Formato MM/YY
    card_last_4: string // Últimos 4 dígitos de la tarjeta
    expiration_date: string // Fecha completa en formato ISO
    status: 'NORMAL' | 'BLOCKED' | 'CANCELED' // Enum para estados de la tarjeta
  }
  user: {
    name: string
    email: string
    enterprise: string | null // Empresa asociada, puede ser null
    clabe: string // CLABE bancaria
  }
}

export type BlockCardResponse = {
  statusCode: number
  message: string
  error: string
  card_status: 'NORMAL' | 'BLOCKED'
}
