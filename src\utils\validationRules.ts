/* eslint-disable @typescript-eslint/no-unused-vars */
import { isValidPhoneNumber, parsePhoneNumberFromString } from 'libphonenumber-js'

export const formUserValidationRules = {
  name: (value: string) => (!value.trim() ? 'El nombre es requerido' : ''),
  email: (value: string) => {
    if (!value.trim()) return 'El correo es requerido'
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'El correo no es válido'
    return ''
  },
  phoneAdmin: (value: string) => {
    if (!value.trim()) return 'El teléfono es requerido'
    try {
      // Asegúrate de que comience con "+"
      const normalizedValue = value.startsWith('+') ? value : `+${value}`
      if (!isValidPhoneNumber(normalizedValue))
        return 'El número no es válido para el país seleccionado'

      const phone = parsePhoneNumberFromString(normalizedValue)
      const length = phone?.nationalNumber.length || 0
      if (length < 6 || length > 15) return 'La longitud del número no es válida para este país'

      return ''
    } catch (err) {
      return 'El número de teléfono no es válido'
    }
  },
  profileType: (value: string) => (!value ? 'El tipo de perfil es requerido' : ''),
  adminPassword: (value: string) => {
    if (!value.trim()) return 'La contraseña es requerida'
    if (value.length < 8) return 'La contraseña debe tener al menos 8 caracteres'
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value))
      return 'La contraseña debe tener al menos una mayúscula, una minúscula y un número'
    return ''
  },
  confirmPassword: (password: string, confirmPassword: string) =>
    password !== confirmPassword ? 'Las contraseñas no coinciden' : '',
}

export const clientValidationRules = {
  companyName: (value: string) => (!value.trim() ? 'El nombre de la compañía es requerido' : ''),
  alias: (value: string) => (!value.trim() ? 'El alias es requerido' : ''),
  rfc: (value: string, isSucursal: boolean = false) => {
    if (isSucursal) return '' // Si es sucursal, se permite cualquier formato
    const rfcMoralRegex = /^[A-Z&Ñ]{3}\d{6}[A-Z\d]{3}$/ // RFC moral estándar
    return !rfcMoralRegex.test(value) ? 'El RFC no es válido según las normas fiscales' : ''
  },
  responsibleName: (value: string) =>
    !value.trim() ? 'El nombre del responsable es requerido' : '',
  state: (value: string) => (!value ? 'El estado es requerido' : ''),
  city: (value: string) => (!value ? 'La ciudad es requerida' : ''),
  registrationDate: (value: string) => (!value ? 'La fecha de alta es requerida' : ''),
  assignedCards: (value: number) => (!value ? 'El número de tarjetas asignadas es requerido' : ''),
  phone: (value: string) => {
    if (!value.trim()) return 'El teléfono es requerido'
    if (!/^\d{10}$/.test(value)) return 'El teléfono debe tener 10 dígitos'
    return ''
  },
  email: (value: string) => {
    if (!value.trim()) return 'El correo es requerido'
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'El correo no es válido'
    return ''
  },
  street: (value: string) => (!value.trim() ? 'La calle es requerida' : ''),
  zipCode: (value: string) => (!value.trim() ? 'El código postal es requerido' : ''),
  exteriorNumber: (value: string) => {
    if (!value.trim()) return ''
    if (!/^[a-zA-Z0-9\s\-\.]+$/.test(value)) return 'Si no cuenta con número exterior, escribe "SN"'
    return ''
  },
  neighborhood: (value: string) => (!value.trim() ? 'La colonia es requerida' : ''),
  passwordClient: (value: string, isUpdate: undefined | boolean) => {
    if (isUpdate && value.trim() === '') return ''
    if (isUpdate && value.trim() !== '') {
      if (value.length < 8) return 'La contraseña debe tener al menos 8 caracteres'
      if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value))
        return 'La contraseña debe tener al menos una mayúscula, una minúscula y un número'
      return ''
    } else {
      if (!value.trim()) return 'La contraseña es requerida'
      if (value.length < 8) return 'La contraseña debe tener al menos 8 caracteres'
      if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value))
        return 'La contraseña debe tener al menos una mayúscula, una minúscula y un número'
      return ''
    }
  },
  confirmPassword: (password: string, confirmPassword: string, isUpdate: undefined | boolean) => {
    if (isUpdate && confirmPassword.trim() === '') return ''
    if (confirmPassword.trim() !== '') {
      if (password !== confirmPassword) {
        return 'Las contraseñas no coinciden'
      } else {
        return ''
      }
    }
  },
  // password !== confirmPassword ? "Las contraseñas no coinciden" : "",
  files: {
    act: (file: File | null) =>
      validateFile(file, {
        extensions: ['pdf', 'jpg', 'jpeg', 'png'],
        mimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      }),
    constancy: (file: File | null) =>
      validateFile(file, {
        extensions: ['pdf', 'jpg', 'jpeg', 'png'],
        mimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      }),
    file: (file: File | null) =>
      validateFile(file, {
        extensions: ['pdf', 'jpg', 'jpeg', 'png'],
        mimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      }),
    other: (file: File | null) =>
      validateFile(file, {
        extensions: ['pdf', 'jpg', 'jpeg', 'png'],
        mimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      }),
  },
  commissionPercentage: (value: string) => {
    const number = parseFloat(value)
    if (!value.trim()) return 'El porcentaje de comisión es requerido'
    if (isNaN(number) || number < 0 || number > 100) return 'Debe ser un número entre 0 y 100'
    if (!hasMaxThreeDecimals(value)) return 'Máximo 3 decimales permitidos'
    return ''
  },
  commissionAmount: (value: string) => {
    const number = parseFloat(value)
    if (!value.trim()) return 'El monto de comisión es requerido'
    if (isNaN(number) || number <= 0) return 'Debe ser un número positivo'
    return ''
  },
  commissionCardFunding: (value: string) => {
    const number = parseFloat(value)
    if (!value.trim()) return 'La comisión por fondeo de tarjeta es requerida'
    if (isNaN(number) || number < 0 || number > 100) return 'Debe ser un número entre 0 y 100'
    if (!hasMaxThreeDecimals(value)) return 'Máximo 3 decimales permitidos'
    return ''
  },
  commissionAmbassador: (value: string) => {
    if (!value.trim()) return 'El nombre del embajador es requerido'
    if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/.test(value)) return 'Solo se permiten letras y espacios'
    return ''
  },
  extraCompanies: (companies: string[]): (string | undefined)[] | undefined => {
    const errors = companies.map(company =>
      !company || company.trim() === '' ? 'Debes seleccionar una empresa' : undefined
    )

    // Devuelve solo si hay errores
    return errors.some(e => e !== undefined) ? errors : undefined
  },
}

// Función para validar que un valor tenga como máximo 3 decimales
const hasMaxThreeDecimals = (value: string): boolean => {
  return /^\d+(\.\d{1,3})?$/.test(value)
}

export const massiveLoadValidationRules = {
  file: (file: File | null) =>
    validateFile(file, {
      extensions: ['csv', 'xls', 'xlsx'],
      mimeTypes: [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ],
    }),
}

export const aliasValidationRules = {
  alias: (value: string) => (!value.trim() ? 'El alias es requerido' : ''),
  responsibleName: (value: string) =>
    !value.trim() ? 'El nombre del responsable es requerido' : '',
  rfc: (value: string) => {
    const rfcRegex = /^[A-ZÑ&]{3,4}\d{6}[A-Z\d]{2}[0-9A]$/
    return !rfcRegex.test(value) ? 'El RFC no es válido según las normas fiscales' : ''
  },
  registrationDate: (value: string) => (!value.trim() ? 'La fecha de alta es requerida' : ''),
  profileType: (value: string) => (!value.trim() ? 'El tipo de perfil es requerido' : ''),
  balance: (value: string) => {
    if (!value.trim()) return 'El saldo es requerido'
    const num = parseFloat(value.replace(/[$,]/g, ''))
    return isNaN(num) ? 'El saldo debe ser numérico' : ''
  },
  extraCompanies: (companies: string[]): (string | undefined)[] | undefined => {
    const errors = companies.map(company =>
      !company || company.trim() === '' ? 'Debes seleccionar una empresa' : undefined
    )

    // Devuelve solo si hay errores
    return errors.some(e => e !== undefined) ? errors : undefined
  },
}

// Función para validar archivos (flexible según el tipo de validación)
const validateFile = (
  file: File | null,
  allowedTypes: { extensions: string[]; mimeTypes: string[] } = { extensions: [], mimeTypes: [] },
  maxSizeMB: number = 5
): string => {
  if (!file) return 'Debe subir un archivo.'

  // Validar la extensión del archivo
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  if (!fileExtension || !allowedTypes.extensions.includes(fileExtension)) {
    return `Formato inválido. Solo se aceptan archivos: ${allowedTypes.extensions.join(', ')}.`
  }

  // Validar el tipo MIME
  if (!allowedTypes.mimeTypes.includes(file.type)) {
    return `Tipo de archivo no permitido. Solo se aceptan archivos: ${allowedTypes.mimeTypes.join(', ')}.`
  }

  // Validar tamaño del archivo
  if (file.size > maxSizeMB * 1024 * 1024) {
    return `El archivo debe pesar menos de ${maxSizeMB}MB.`
  }

  return '' // No hay error si el archivo es válido
}

export const accountValidationRules = {
  name: (value: string) => (!value.trim() ? 'El nombre es requerido' : ''),
  email: (value: string) => {
    if (!value.trim()) return 'El correo es requerido'
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'El correo no es válido'
    return ''
  },
  accountNumber: (value: string) => (!value.trim() ? 'El número de cuenta es requerido' : ''),
  companyName: (value: string) => (!value.trim() ? 'El alias de la compañía es requerido' : ''),
  phone: (value: string) => {
    if (!value.trim()) return 'El teléfono es requerido'
    if (!/^\d{10}$/.test(value)) return 'El teléfono debe tener 10 dígitos'
    return ''
  },
  registrationDate: (value: string) => (!value ? 'La fecha de alta es requerida' : ''),
  affiliationNumber: (value: string) =>
    !value.trim() ? 'El número de cuenta Convenia es requerido' : '',
}

export const transferValidationRules = {
  empresa: (value: string) => (value === '0' ? 'Elige la empresa' : ''),
  numeroCuenta: (value: string) => {
    if (!value.trim())
      return 'Escribe una Cuenta CONVENIA/CLABE interbancaria/Número de tarjeta que corresponda con la empresa seleccionada'
    if (!/^[A-Za-z0-9-]+$/.test(value)) return 'Solo se permiten números'
    return ''
  },
  cuentaDestino: (value: string) => {
    if (!value.trim()) return 'Ingresa una cuenta válida'
    if (!/^\d+$/.test(value)) return 'Solo se permiten números'
    return ''
  },
  tipoTransferencia: (value: string) =>
    !value.trim() ? 'El tipo de transferencia es requerido' : '',
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  banco: (value: string) => '', // Opcional
  numeroReferencia: (value: string) => {
    if (!value.trim()) return '' // Ahora es opcional
    if (!/^\d+$/.test(value)) return 'Solo se permiten números'
    return ''
  },
  importe: (value: string) => {
    const number = parseFloat(value)
    if (!value.trim()) return 'Escribe un importe correcto'
    if (isNaN(number) || number <= 0) return 'Debe ser un número positivo'
    return ''
  },
  nombreBeneficiario: (value: string) =>
    !value.trim() ? 'Escribe el nombre del beneficiario' : '',
  rfcBeneficiario: (value: string) => {
    if (!value.trim()) return '' // Opcional
    if (value.length < 12 || value.length > 13) return 'El RFC debe tener 12 o 13 caracteres'
    const rfcRegex = /^[A-ZÑ&]{3,4}\d{6}[A-Z\d]{2}[0-9A]$/
    return !rfcRegex.test(value) ? 'El RFC no es válido según las normas fiscales' : ''
  },
  correo: (value: string) => {
    if (!value.trim()) return '' // Opcional
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'El correo no es válido'
    return ''
  },
  conceptoBeneficiario: (value: string) => (!value.trim() ? 'Escribe un concepto váldo' : ''),
  tipoPago: (value: string) => (value === '0' ? 'Debe elegir un tipo de pago' : ''),
}

export const editContactValidationRules = {
  cuentaDestino: (value: string) => {
    if (!value.trim()) return 'La cuenta destino es requerida'
    if (!/^\d+$/.test(value)) return 'Solo se permiten números'
    return ''
  },
  nombreDelBeneficiario: (value: string) =>
    !value.trim() ? 'El nombre del beneficiario es requerido' : '',
  rfcBeneficiario: (value: string) => {
    if (!value.trim()) return '' // Opcional
    if (value.length < 12 || value.length > 13) return 'El RFC debe tener 12 o 13 caracteres'
    const rfcRegex = /^[A-ZÑ&]{3,4}\d{6}[A-Z\d]{2}[0-9A]$/
    return !rfcRegex.test(value) ? 'El RFC no es válido según las normas fiscales' : ''
  },
  correo: (value: string) => {
    if (!value.trim()) return '' // Opcional
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'El correo no es válido'
    return ''
  },
  aliasName: (value: string) => (!value.trim() ? 'El nombre del alias es requerido' : ''),
}
