import {
  AdminBasic,
  AdminData,
  AdminListParams,
  AdminResponse,
  AdminClient,
  AdminDetail,
  AdminByUserIdClient,
  AdminAmount,
  MassiveAdminResponse,
} from '@/types/admin/types'
import apiClient from '../client'

export const createAdmin = async (data: Partial<AdminData>): Promise<AdminResponse> => {
  const response = await apiClient.post('/admin', data)
  return response.data
}

export const getAdmins = async (
  params: AdminListParams
): Promise<{ admins: AdminClient[]; count: number }> => {
  const response = await apiClient.get('/admin', { params })
  return response.data
}

export const getAdminsByUserId = async (params: {
  userId: string
}): Promise<{ admins: AdminByUserIdClient[]; count: number }> => {
  const response = await apiClient.get('/admin/allByUserId', { params })

  // Verifica que `admin` sea un array y puedes obtener la cantidad directamente
  const admins: AdminByUserIdClient[] = response.data.data.admin || []

  return {
    admins,
    count: admins.length,
  }
}

export const getAdminById = async (id: string): Promise<AdminDetail> => {
  const response = await apiClient.get(`/admin/${id}`)
  return response.data
}

export const getAdminByRFC = async (rfc: string): Promise<AdminResponse> => {
  const response = await apiClient.get(`/admin/rfc/${rfc}`)
  return response.data
}

export const getAdminByMembership = async (membership_number: number): Promise<AdminResponse> => {
  const response = await apiClient.get(`/admin/findAdminByMembershipNumber/${membership_number}`)
  return response.data
}

export const getBasicAdmins = async (): Promise<AdminBasic[]> => {
  const response = await apiClient.get('/admin/basic')
  return response.data
}

export const getAdminAmount = async ({ adminId, groupId }: AdminAmount) => {
  const response = await apiClient.get('/admin/amount', {
    params: {
      ...(adminId && { adminId }),
      ...(groupId && { groupId }),
    },
  })

  const totalAmount = parseFloat(response.data.totalAmount)

  return { amount: totalAmount }
}

export const updateAdmin = async (id: string, data: Partial<AdminData>): Promise<AdminResponse> => {
  const response = await apiClient.patch(`/admin/${id}`, data)
  return response.data
}

export const deleteAdmin = async (id: string): Promise<{ message: string }> => {
  const response = await apiClient.delete(`/admin/${id}`)
  return response.data
}

export const getAmountTransfer = async () => {
  const response = await apiClient.post('/transfer-orders/balance-account')
  const availableBalance = response.data.data.response.data.availableBalance
  return { availableBalance }
}

export const getTotalActiveCards = async (id: string | null) => {
  const response = await apiClient.get(`admin/${id}/total-cards`)
  const totalActiveCards = response.data.total
  return {
    totalActiveCards
  }
}

export const createMassiveAdmin = async (data: {
  creatorEmail: string
  name: string
  admins: Partial<AdminData>[]
}): Promise<MassiveAdminResponse> => {
  const response = await apiClient.post('/admin/massive', data)
  return response.data
}
