'use client'
import { useState, useRef, useEffect } from 'react'
import { LuCalendar } from 'react-icons/lu'
import { DayPicker } from 'react-day-picker'
import 'react-day-picker/dist/style.css'
import styles from './styles/Calendar.module.css'

type Props = {
  onDateChange: (dates: string[]) => void
}

const DateFilter = ({ onDateChange }: Props) => {
  const [selectedDates, setSelectedDates] = useState<Date[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleDayClick = (date: Date) => {
    const isSame = selectedDates[0]?.toDateString() === date.toDateString()
    const updated = isSame ? [] : [date]

    setSelectedDates(updated)

    const formatted = updated.map(d => d.toISOString().split('T')[0])
    onDateChange(formatted)
  }

  // Cierra si se hace clic fuera
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={styles.dropdownContainer} ref={containerRef}>
      <button className={styles.toggleButton} onClick={() => setIsOpen(!isOpen)}>
        <LuCalendar size={20} /> Fechas
      </button>
      {isOpen && (
        <div className={styles.calendarWrapper}>
          <DayPicker
            mode="single"
            selected={selectedDates[0]}
            onDayClick={handleDayClick}
            modifiersClassNames={{
              selected: styles.goldPill,
            }}
            className={styles.customCalendar}
            showOutsideDays
            fixedWeeks
          />
        </div>
      )}
    </div>
  )
}

export default DateFilter
