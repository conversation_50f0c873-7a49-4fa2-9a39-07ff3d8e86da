'use client'

import React, { useEffect, useState } from 'react'
import styles from '../styles/Clients.module.css'
import AccountsTable from '../cuentas/components/AccountsTable'
import { useRouter } from 'next/navigation'
import { useAccountStore } from '@/store/account/useAccountStore'
import { AccountResponse } from '@/types/account/types'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { shouldSendAdminId } from '@/permissions/access'
import { RoleName } from '@/constants/roles'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import ModalDeleteAccount from '../modals/alertModals/ModalDeleteAccount'

const LastAccountsCreated = () => {
  const router = useRouter()
  const { accounts, clearSelected, fetchAccounts, deleteAccount } = useAccountStore()
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [accountToDelete, setAccountToDelete] = useState('')
  const [userIdToDelete, setUserIdToDelete] = useState('')

  const roleName = user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name || null
  const adminId = shouldSendAdminId(roleName as RoleName | null)
    ? user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null
    : null

  useEffect(() => {
    clearSelected()
    fetchAccounts({
      limit: 5,
      page: 1,
      q: '',
      adminId
    })
  }, [clearSelected, fetchAccounts, adminId])

  const handleViewAccount = (account: AccountResponse) => {
    router.push(`/cuentas/detalles-cuenta?accountId=${account.email}`)
  }

  const handleEditAccount = (account: AccountResponse) => {
    router.push(`/cuentas/editar-cuenta?accountId=${account.id}`)
  }

  const handleActiveAccount = () => {
    // Aquí podrías dejarlo vacío o solo loguear si no necesitas esta función
  }

  const handleDeleteAccount = (account: AccountResponse) => {
    setAccountToDelete(account.clabe)
    setUserIdToDelete(account.id)
    setDeleteModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    try {
      await deleteAccount(userIdToDelete)
      // Refetch accounts after successful deletion
      await fetchAccounts({
        limit: 5,
        page: 1,
        q: '',
        adminId
      })
      setDeleteModalOpen(false)
    } catch (error) {
      console.error('Error deleting account:', error)
    }
  }

  const handleOpenSpeiModal = () => {
    // Lógica vacía si no es necesaria
  }

  return (
    <div>
      <h3 className={styles.title}>Últimas cuentas creadas</h3>
      <AccountsTable
        accounts={accounts}
        onViewAccount={handleViewAccount}
        onActiveAccount={handleActiveAccount}
        onEditAccount={handleEditAccount}
        onDeleteAccount={handleDeleteAccount}
        onOpenSpeiModal={handleOpenSpeiModal}
      />
      <ModalDeleteAccount
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        accountNumber={accountToDelete}
        onConfirm={handleConfirmDelete}
      />
    </div>
  )
}

export default LastAccountsCreated
