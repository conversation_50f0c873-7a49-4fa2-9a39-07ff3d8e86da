'use client'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../../styles/AccountsTable.module.css'
import { AccountsTableProps } from '@/types/types'
import ToggleBlock from './ToggleBlock'
import ActivateButton from './ActivateButton'
import Image from 'next/image'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { RoleName, ROLES } from '@/constants/roles'
import { AccountResponse } from '@/types/account/types'

type TableColumn = {
  key: string
  label: string
  allowedRoles?: RoleName[]
  render: (account: AccountResponse) => React.ReactNode
}

const AccountsTable: React.FC<AccountsTableProps> = ({
  accounts,
  onViewAccount,
  onActiveAccount,
  onEditAccount,
  onDeleteAccount,
  onOpenSpeiModal,
}) => {
  const { getUserRoleName } = useAuthStore()

  const columns: TableColumn[] = [
    {
      key: 'user',
      label: 'Usuario',
      render: (account: AccountResponse) => (
        <div className={styles.accountCell}>
          <Image
            src="/logo-accounts-convenia.svg"
            alt="Convenia logo"
            width={40}
            height={40}
            className={styles.accountAvatar}
          />
          <div className={styles.accountInfo}>
            <strong>{account.name}</strong>
            {account.email && <span>{account.email}</span>}
          </div>
        </div>
      ),
    },
    {
      key: 'clabe',
      label: 'Numero de cuenta',
      render: (account: AccountResponse) => account.clabe,
    },
    {
      key: 'alias',
      label: 'Alias empresa',
      render: (account: AccountResponse) => account.adminAlias,
    },
    {
      key: 'saldo',
      label: 'Saldo de cuenta',
      render: (account: AccountResponse) =>
        typeof account.amount === 'number'
          ? `$${account.amount.toLocaleString('es-MX', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`
          : 'Sin saldo',
    },
    {
      key: 'speiIn',
      label: 'SPEI (IN)',
      allowedRoles: [ROLES.ADMIN_CONVENIA],
      render: (account: AccountResponse) => (
        <ToggleBlock
          value={account.speiIn ?? false}
          onToggle={() =>
            onOpenSpeiModal?.(
              account.clabe,
              { speiIn: !(account.speiIn ?? false), speiOut: account.speiOut ?? false },
              'in',
              account.id
            )
          }
        />
      ),
    },
    {
      key: 'speiOut',
      label: 'SPEI (OUT)',
      allowedRoles: [ROLES.ADMIN_CONVENIA],
      render: (account: AccountResponse) => (
        <ToggleBlock
          value={account.speiOut ?? false}
          onToggle={() =>
            onOpenSpeiModal?.(
              account.clabe,
              { speiIn: account.speiIn ?? false, speiOut: !(account.speiOut ?? false) },
              'out',
              account.id
            )
          }
        />
      ),
    },
    {
      key: 'actions',
      label: 'Acciones',
      allowedRoles: [ROLES.ADMIN_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN, ROLES.CLIENTE_TARJETAHABIENTES],
      render: (account: AccountResponse) => (
        <div className={styles.actions}>
          {!account.enabled && (
            <ActivateButton status={false} onRequestToggle={() => onActiveAccount?.(account)} />
          )}
          {getUserRoleName() !== ROLES.CLIENTE_EMPRESA_ADMIN && (
            <button className={styles.actionButton} onClick={() => onEditAccount(account)}>
              <MdOutlineEdit size={18} />
            </button>
          )}
          {account.isDeleteable &&
            <button className={styles.actionButton} onClick={() => onDeleteAccount(account)}>
              <LuTrash2 size={18} />
            </button>
          }
        </div>
      ),
    },
  ]

  const visibleColumns = columns.filter(
    column => !column.allowedRoles || column.allowedRoles.includes(getUserRoleName()!)
  )

  return (
    <table className={styles.table}>
      <thead>
        <tr>
          {visibleColumns.map(column => (
            <th key={column.key}>{column.label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {accounts?.length > 0 ? (
          accounts.map((account, index) => (
            <tr key={index}>
              {visibleColumns.map(column => (
                <td
                  key={column.key}
                  onClick={column.key === 'user' ? () => onViewAccount?.(account) : undefined}
                >
                  {column.render(account)}
                </td>
              ))}
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

export default AccountsTable
