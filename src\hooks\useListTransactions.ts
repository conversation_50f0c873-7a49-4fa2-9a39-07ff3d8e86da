import { getListTransactions } from '@/api/endpoints/user'
import { formatCurrency, formatDate, formatTime } from '@/utils/formatters'
import { useState, useEffect } from 'react'

export interface Transaction {
  id: string // Identificador único de la transacción
  date: string // Fecha de la transacción, en formato ISO
  dateFormatted: string // Fecha formateada
  timeFormatted: string // Hora formateada
  description: string // Descripción de la transacción
  amount: string // Monto de la transacción
  key: string
  type: string
}

function getThreeMonthRange() {
  const now = new Date()
  const start = new Date(now)
  start.setMonth(now.getMonth() - 3)
  start.setHours(0, 0, 0, 0) // Inicio del día

  return {
    initialDate: start.toISOString(),
    endDate: now.toISOString(), // Momento actual
  }
}

export const useListTransactions = (email: string) => {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true)
      try {
        const { initialDate, endDate } = getThreeMonthRange()
        const page = 0
        const limit = 100

        const data = await getListTransactions(email, {
          initialDate,
          endDate,
          page,
          limit,
        })
        const formattedData = data
          .map((transaction: Transaction) => {
            const formattedAmount =
              transaction.type === 'creditor'
                ? `+${formatCurrency(String(transaction.amount))}` // "+" si es "creditor"
                : `-${formatCurrency(String(transaction.amount))}` // "-" si es "debtor"

            return {
              ...transaction,
              dateFormatted: formatDate(transaction.date), // Formatear la fecha
              timeFormatted: formatTime(transaction.date),
              amount: formattedAmount, // Usar el monto formateado con el signo correcto
            }
          })
          .sort(
            (a: Transaction, b: Transaction) =>
              new Date(b.date).getTime() - new Date(a.date).getTime()
          ) // Ordena de más reciente a menos reciente
        setTransactions(formattedData)
      } catch (err) {
        console.error('Error fetching transactions:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (email) {
      fetchTransactions()
    }
  }, [email])

  return { transactions, isLoading }
}
