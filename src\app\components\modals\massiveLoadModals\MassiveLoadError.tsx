import React from 'react'
import AlertModal from '../AlertModal'

type MassiveLoadErrorProps = {
  open: boolean
  onClose: () => void
  errors?: string[] // errores opcionales
}

const MassiveLoadError: React.FC<MassiveLoadErrorProps> = ({ open, onClose, errors = [] }) => {
  return (
    <AlertModal
      type="error"
      title="¡No se pudo cargar la información!"
      message="Revisa el archivo e inténtalo de nuevo."
      open={open}
      onClose={onClose}
      textBtn="Aceptar"
      renderMessage={() => (
        <div style={{ 
          marginTop: '12px', 
          whiteSpace: 'pre-line',
          maxHeight: '300px',
          overflowY: 'auto',
          paddingRight: '8px'
        }}>
          {errors.map((error, index) => (
            <p key={index} style={{ fontSize: '14px', color: '#000', marginBottom: 4 }}>
              *{error}
            </p>
          ))}
        </div>
      )}
      onPressBtn={onClose}
    />
  )
}

export default MassiveLoadError
