.appbar {
  width: 100%;
  position: relative;
  background: #000; /* Fondo negro */
  color: #fff; /* Texto blanco */
  display: flex;
  height: 10dvh;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.dropdown {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: max-content;
  background: #000;
  position: absolute;
  right: 0;
  z-index: 2;
  padding: 1rem 0.5rem;
}

.dropdown ul {
  list-style: none;
  padding: 0;
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem 1rem;
  border-radius: 10px; /* Bordes redondeados */
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
}

.menuItem:hover {
  background-color: #333; /* Fondo más claro al pasar el cursor */
}

.active {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #232429;
}

.icon {
  color: #fff; /* Color blanco para los iconos */
}

.iconActive {
  color: #232429;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
}

.userInfo p {
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
}

.logoutButton {
  background: none;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-top: 2px solid gray !important;
  border: none;
  color: #fff; /* Blanco para el icono */
  cursor: pointer;
  transition: color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
}

@media screen and (min-width: 1150px) {
  .appbar {
    display: none;
  }
}
