'use client'
import styles from '../styles/Clients.module.css'
import Pagination from '../Pagination'
import AccountsTable from './components/AccountsTable'
import SearchBar from '@/app/components/SearchBar'
import { useRouter } from 'next/navigation'
import DateFilter from '../DateFilter'
import { AccountResponse } from '@/types/account/types'
import { useAccountStore } from '@/store/account/useAccountStore'

const Accounts = ({
  onToggleStatus,
  accountsData,
  totalPages,
  currentPage,
  onOpenDeleteAccountModal,
  onOpenSpeiModal,
  onSetCurrentPage,
  onSetSearchTerm,
  onSetSelectedDates,
  itemsPerPage,
  onSetItemsPerPage,
}: {
  onToggleStatus: (id: string, accountNumber: string, isActive: boolean) => void
  accountsData: AccountResponse[]
  totalPages: number
  currentPage: number
  onOpenDeleteAccountModal: (accountNumber: string, userId: string) => void
  onOpenSpeiModal: (
    accountNumber: string,
    updateData: { speiIn: boolean; speiOut: boolean },
    type: 'in' | 'out',
    id: string
  ) => void
  onSetCurrentPage: (page: number) => void
  onSetSearchTerm: (term: string) => void
  onSetSelectedDates: (dates: string[]) => void
  itemsPerPage?: number
  onSetItemsPerPage?: (itemsPerPage: number) => void
}) => {
  const router = useRouter() // Hook para redireccionar
  const { setAccountSelected } = useAccountStore()
  const currentAccounts = accountsData

  const handlePrevPage = () => {
    if (currentPage > 1) onSetCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) onSetCurrentPage(currentPage + 1)
  }

  const handleSearch = (value: string) => {
    onSetSearchTerm(value) // Actualiza el término de búsqueda
    onSetCurrentPage(1) // Reinicia la paginación al realizar una búsqueda
  }

  const handleDateFilter = (dates: string[]) => {
    onSetSelectedDates(dates)
    onSetCurrentPage(1)
  }

  const handleActiveAccount = (account: AccountResponse) => {
    onToggleStatus(account.id, account.clabe, account.enabled)
  }

  const handleViewAccount = (account: AccountResponse) => {
    setAccountSelected(account)
    router.push(`/cuentas/${account.id}?${account.isDeleteable ? 'admin=true' : 'admin=false'}`)
  }

  const handleEditAccount = (account: AccountResponse) => {
    setAccountSelected(account)
    router.push('/cuentas/editar-cuenta')
  }

  const handleDeleteAccount = (account: AccountResponse) => {
    onOpenDeleteAccountModal(account.clabe, account.id)
  }

  return (
    <div>
      <div className={styles.header}>
        <div className={styles.newClientContainer}>
          <DateFilter onDateChange={handleDateFilter} />
          <SearchBar placeholder="Buscar" onSearch={handleSearch} />
        </div>
      </div>
      <h3 className={styles.title}>Últimas cuentas creadas</h3>
      <AccountsTable
        accounts={currentAccounts}
        onViewAccount={handleViewAccount}
        onActiveAccount={handleActiveAccount}
        onEditAccount={handleEditAccount}
        onDeleteAccount={handleDeleteAccount}
        onOpenSpeiModal={onOpenSpeiModal}
      />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={onSetCurrentPage}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={onSetItemsPerPage}
      />
    </div>
  )
}

export default Accounts
