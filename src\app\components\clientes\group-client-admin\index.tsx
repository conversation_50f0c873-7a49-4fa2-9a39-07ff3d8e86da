'use client'
import React, { useEffect, useState } from 'react'
import Header from '../../Header'
import ClientsGroup from './ClientsGroup'
import { FaArrowLeft } from 'react-icons/fa'
import styles from '../../styles/Clientes.module.css'
import { useRouter } from 'next/navigation'
import Stats from '../../home/<USER>'
import ModalDeleteAlias from '../../modals/alertModals/ModalDeleteAlias'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { AdminClient } from '@/types/admin/types'

const Content = () => {
  const router = useRouter()
  const { adminsByGroupId, adminClientSelected, amount, fetchAmount, fetchAdminById } =
    useAdminStore()
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [clientToDelete, setClientToDelete] = useState<AdminClient | null>(null)

  useEffect(() => {
    if (adminClientSelected) {
      fetchAmount({ groupId: adminClientSelected.groupId })
    }
  }, [adminClientSelected, fetchAmount])

  const handleBack = () => router.push('/clientes')

  const handleOpenDelete = (client: AdminClient) => {
    setClientToDelete(client)
    setOpenDeleteModal(true)
  }

  const handleCloseDelete = () => {
    setOpenDeleteModal(false)
    setClientToDelete(null)
  }

  const handleConfirmDelete = () => {
    // if (clientToDelete) {
    //   const updatedAccounts = accounts.filter(account => account.alias !== clientToDelete.alias)
    //   setAccounts(updatedAccounts)
    // }
    handleCloseDelete()
  }

  const handleEditClient = (client: AdminClient) => {
    fetchAdminById(client.id)
    router.push(`/clientes/group-client-admin/edit-alias`)
  }

  const handleViewClient = (client: AdminClient) => {
    fetchAdminById(client.id)
    router.push(`/clientes/group-client-admin/view-alias`)
  }

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`
  }
  return (
    <>
      <Header title={adminClientSelected?.companyName || 'Cliente no encontrado'} />
      <button className={styles.backButton} onClick={handleBack}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>
      <Stats stats={[{ label: 'Saldo Total', value: formatCurrency(amount) }]} />
      <ClientsGroup
        clientData={adminsByGroupId}
        onDeleteClient={handleOpenDelete}
        onEditClient={handleEditClient}
        onViewClient={handleViewClient}
      />
      <ModalDeleteAlias
        open={openDeleteModal}
        onClose={handleCloseDelete}
        onConfirm={handleConfirmDelete}
        name={clientToDelete?.alias || ''}
      />
    </>
  )
}

export default Content
