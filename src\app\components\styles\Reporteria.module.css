.container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.label {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #4a4b55;
}

.content {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  user-select: all;
}

.formContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formContainer label {
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.formGroup {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: end;
  width: 100%;
  gap: 1rem;
  margin-bottom: 3rem;
}

.resetButton {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  align-items: center;
  border: 1px solid #b4915f !important;
  margin-bottom: 1rem !important;
}

.badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background-color: #edf3d9;
  border-radius: 10px;
}

.formGroupButtons {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 1rem;
  margin-bottom: 1rem;
}