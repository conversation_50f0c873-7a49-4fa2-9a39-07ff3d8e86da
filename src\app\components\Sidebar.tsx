'use client'

import Logo from './Logo'
import { usePathname, useRouter } from 'next/navigation'
import styles from './styles/Sidebar.module.css'
import Image from 'next/image'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { getVisibleMenuItemsByRole } from '@/constants/menu'
import { FiLogOut } from 'react-icons/fi'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'


const Sidebar = ({ onLogoutClick }: { onLogoutClick: () => void }) => {
  const router = useRouter()
  const pathname = usePathname() // Obtiene la ruta actual

  const { user, getUserRole, getUserRoleName } = useAuthStore()
  const role = getUserRole()
  const roleName = getUserRoleName()

  const visibleMenuItems = getVisibleMenuItemsByRole(roleName)

  const { selectedAdminIndex, setSelectedAdminIndex } = useAdminUiStore()

  const handleLogout = () => {
    onLogoutClick()
  }

  return (
    <aside className={styles.sidebar}>
      {/* Logo */}
      <div className={styles.logoContainer}>
        <Logo />
      </div>
      <div className={styles.profileContainer}>
        <p className={styles.profileLabel}>Perfil: {role?.name || 'Sin rol asignado'}</p>
      </div>

      {/* Menú de navegación */}
      <nav className={styles.menu}>
        <ul>
          {visibleMenuItems.map(({ path, label, icon: Icon }) => {
            // Comprobamos si el pathname comienza con el path para detectar subrutas
            const isActive = pathname.startsWith(path)

            return (
              <li
                key={path}
                className={`${styles.menuItem} ${isActive ? styles.active : ''}`}
                onClick={() => router.push(path)}
              >
                <Icon size={24} className={`${styles.icon} ${isActive ? styles.iconActive : ''}`} />
                {label}
              </li>
            )
          })}
          {/* Selector de empresas */}
          {(user?.relUserRoleAdmins?.length ?? 0) > 1 && (
            <div className={styles.companySelector}>
              <p className={styles.sectionTitle}>Empresas</p>
              <ul className={styles.companyList}>
                {user?.relUserRoleAdmins?.map((rel, index) => {
                  if (!rel.admin) return null
                  return (
                    <li
                      key={rel.admin.id}
                      className={`${styles.companyItem} ${index === selectedAdminIndex ? styles.activeCompany : ''
                        }`}
                      onClick={() => setSelectedAdminIndex(index)}
                    >
                      {rel.admin.alias}
                    </li>
                  )
                })}
              </ul>
            </div>
          )}
        </ul>


      </nav>

      {/* Información de perfil */}
      <div className={styles.profile}>
        <div className={styles.userInfo}>
          <Image
            src="/icon-convenia-user.svg"
            alt="Icono"
            className={styles.icon}
            width={40}
            height={40}
          />
          <p title={user?.email}>{user?.email || 'sin correo'}</p>
        </div>
        <button className={styles.logoutButton} onClick={handleLogout}>
          <FiLogOut size={20} />
        </button>
      </div>
    </aside>
  )
}

export default Sidebar
