.dropdownContainer {
  position: relative;
  display: inline-block;
}

.toggleButton {
  height: 48px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  border: 1px solid #cbd5e0;
  border-radius: 12px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 16px;
  color: #667085;
  transition: background 0.2s;
}

.toggleButton:hover {
  background-color: #f1f1f1;
}

.calendarWrapper {
  position: absolute;
  top: 110%;
  left: 0;
  z-index: 100;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 1rem;
}

.customCalendar {
  font-family: inherit;
  width: 100%;
  max-width: 320px;
}

.goldPill {
  border-radius: 50px;
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: white !important;
  font-weight: 600;
}

.rangeStart,
.rangeEnd,
.rangeMiddle {
  background: linear-gradient(90deg, #d4af7f, #a07b42);
  color: white !important;
  border-radius: 999px;
  font-weight: 600;
}

.selected .dayButton {
  border: none !important;
}
