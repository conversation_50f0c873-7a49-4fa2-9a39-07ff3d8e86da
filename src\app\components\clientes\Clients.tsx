'use client'
import styles from '../styles/Clients.module.css'
import Pagination from '../Pagination'
import Button from '@/app/components/Button'
import ClientsTable from './components/ClientsTable'
import SearchBar from '@/app/components/SearchBar'
import { AdminClient } from '@/types/admin/types'

interface ClientsProps {
  admins: AdminClient[]
  totalPages: number
  currentPage: number
  searchTerm: string
  onSearch: (value: string) => void
  onPrevPage: () => void
  onNextPage: () => void
  onNavigatePage: (page: number) => void
  onNewClient: () => void
  onDeleteClient: (client: AdminClient) => void
  onEditClient: (client: AdminClient) => Promise<void>
  onViewClient: (client: AdminClient) => void
}

const Clients: React.FC<ClientsProps> = ({
  admins,
  totalPages,
  currentPage,
  onSearch,
  onPrevPage,
  onNextPage,
  onNavigatePage,
  onNewClient,
  onDeleteClient,
  onEditClient,
  onViewClient
}) => {
  return (
    <div>
      <div className={styles.header}>
        <div className={styles.newClientContainer}>
          <Button text="Nuevo cliente" onClick={onNewClient} />
          <SearchBar placeholder="Buscar clientes" onSearch={onSearch} />
        </div>
      </div>
      <ClientsTable
        clients={admins}
        onViewClient={onViewClient}
        onEditClient={onEditClient}
        onDeleteClient={onDeleteClient}
      />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={onPrevPage}
        onNextPage={onNextPage}
        onNavigatePage={onNavigatePage}
      />
    </div>
  )
}

export default Clients
