'use client'
// import { useState } from 'react'
import styles from '../../styles/Clients.module.css'
import Pagination from '../../Pagination'
import { ClientsGroupProps } from '@/types/types'
import SearchBar from '@/app/components/SearchBar'
import ClientsGroupTable from '../components/ClientsGroupTable'

const ClientsGroup: React.FC<ClientsGroupProps> = ({
  clientData,
  onDeleteClient,
  onEditClient,
  onViewClient,
}) => {
  // const [currentPage, setCurrentPage] = useState(1)
  // const [searchTerm, setSearchTerm] = useState('') // Estado para manejar la búsqueda

  // const itemsPerPage = 5

  // Filtra los datos de los clientes según el término de búsqueda
  // const filteredClients = clientData.filter(client =>
  //   [client.alias, client.responsibleName, client.profileType, client.conveniaAccount].some(field =>
  //     field.toLowerCase().includes(searchTerm.toLowerCase())
  //   )
  // )

  // Calcula los índices para la paginación
  // const startIndex = (currentPage - 1) * itemsPerPage
  // const endIndex = startIndex + itemsPerPage
  // const currentClients = filteredClients.slice(startIndex, endIndex)

  // Calcula el número total de páginas
  // const totalPages = Math.ceil(filteredClients.length / itemsPerPage)

  // const handlePrevPage = () => {
  //   if (currentPage > 1) setCurrentPage(currentPage - 1)
  // }

  // const handleNextPage = () => {
  //   if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  // }

  // const handleSearch = (value: string) => {
  //   // setSearchTerm(value) // Actualiza el término de búsqueda
  //   // setCurrentPage(1) // Reinicia la paginación al realizar una búsqueda
  // }

  return (
    <div>
      <div className={styles.header}>
        <div className={styles.newClientContainer}>
          <SearchBar placeholder="Buscar" onSearch={() => null} />
        </div>
      </div>
      <ClientsGroupTable
        clients={clientData}
        onViewClient={onViewClient}
        onEditClient={onEditClient}
        onDeleteClient={onDeleteClient}
      />
      <Pagination
        currentPage={1}
        totalPages={1}
        onPrevPage={() => null}
        onNextPage={() => null}
        onNavigatePage={() => null}
      />
    </div>
  )
}

export default ClientsGroup
