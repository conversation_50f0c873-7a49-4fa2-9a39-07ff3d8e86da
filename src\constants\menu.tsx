import { RiHome6Line, RiAdminLine } from 'react-icons/ri'
import { PiUserLight } from 'react-icons/pi'
import { TbCards, TbChartBarPopular } from 'react-icons/tb'
import { MdOutlineAccountBox } from 'react-icons/md'
import { BiConversation } from 'react-icons/bi'
import Transferencias from '../app/components/icons/TransferenciasIcon'
import { ROLES } from './roles'

export const MENU_ROUTES = {
  HOME: '/home',
  GESTION_USUARIOS: '/gestion-usuarios',
  CLIENTES: '/clientes',
  COMISIONES: '/comisiones',
  TRANSFERENCIAS: '/transferencias',
  CUENTAS: '/cuentas',
  REPORTERIA: '/reporteria',
  ACLARACIONES: '/aclaraciones',
} as const

export const MENU_LIST = [
  { path: MENU_ROUTES.HOME, label: 'Home', icon: RiHome6Line },
  { path: MENU_ROUTES.GESTION_USUARIOS, label: 'Gestión de usuarios', icon: RiAdminLine },
  { path: MENU_ROUTES.CLIENTES, label: 'Clientes', icon: PiUserLight },
  { path: MENU_ROUTES.COMISIONES, label: 'Comisiones', icon: TbCards },
  { path: MENU_ROUTES.TRANSFERENCIAS, label: 'Transferencias', icon: Transferencias },
  { path: MENU_ROUTES.CUENTAS, label: 'Cuentas', icon: MdOutlineAccountBox },
  { path: MENU_ROUTES.REPORTERIA, label: 'Reportería', icon: TbChartBarPopular },
  { path: MENU_ROUTES.ACLARACIONES, label: 'Aclaraciones', icon: BiConversation },
]

export const MENU_VISIBILITY_BY_ROLE: Record<string, string[]> = {
  [ROLES.ADMIN_CONVENIA]: [
    MENU_ROUTES.HOME,
    MENU_ROUTES.GESTION_USUARIOS,
    MENU_ROUTES.CLIENTES,
    MENU_ROUTES.COMISIONES,
    MENU_ROUTES.TRANSFERENCIAS,
    MENU_ROUTES.CUENTAS,
    MENU_ROUTES.REPORTERIA,
    MENU_ROUTES.ACLARACIONES,
  ],
  [ROLES.ADMIN_CONVENIA_CONSULTOR]: [
    MENU_ROUTES.HOME,
    MENU_ROUTES.GESTION_USUARIOS,
    MENU_ROUTES.CLIENTES,
    MENU_ROUTES.CUENTAS,
    MENU_ROUTES.REPORTERIA,
  ],
  [ROLES.SOPORTE_CONVENIA]: [MENU_ROUTES.ACLARACIONES],
  [ROLES.CLIENTE_EMPRESA_ADMIN]: [
    MENU_ROUTES.HOME,
    MENU_ROUTES.GESTION_USUARIOS,
    MENU_ROUTES.TRANSFERENCIAS,
    MENU_ROUTES.CUENTAS,
    MENU_ROUTES.REPORTERIA,
  ],
  [ROLES.CLIENTE_EMPRESA_TESORERO]: [
    MENU_ROUTES.HOME,
    MENU_ROUTES.TRANSFERENCIAS,
    MENU_ROUTES.CUENTAS,
    MENU_ROUTES.REPORTERIA,
  ],
  [ROLES.CLIENTE_TARJETAHABIENTES]: [MENU_ROUTES.CUENTAS, MENU_ROUTES.TRANSFERENCIAS],
  [ROLES.CLIENTE_LECTOR]: [MENU_ROUTES.CUENTAS, MENU_ROUTES.REPORTERIA],
}

export const getVisibleMenuItemsByRole = (roleName: string | null) => {
  const visiblePaths = MENU_VISIBILITY_BY_ROLE[roleName || ''] || []

  return MENU_LIST.filter(item => visiblePaths.includes(item.path))
}
