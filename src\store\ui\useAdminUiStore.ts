import { create } from 'zustand'

interface AdminUiStore {
    selectedAdminIndex: number
    setSelectedAdminIndex: (index: number) => void
    removeSelectedAdminIndex: () => void
}

const STORAGE_KEY = 'selectedAdminIndex'

export const useAdminUiStore = create<AdminUiStore>((set) => {
    const storedIndex = typeof window !== 'undefined'
        ? parseInt(localStorage.getItem(STORAGE_KEY) || '0', 10)
        : 0

    return {
        selectedAdminIndex: isNaN(storedIndex) ? 0 : storedIndex,

        setSelectedAdminIndex: (index: number) => {
            localStorage.setItem(STORAGE_KEY, index.toString())
            set({ selectedAdminIndex: index })
        },

        removeSelectedAdminIndex: () => {
            if (typeof window !== 'undefined') {
                localStorage.removeItem(STORAGE_KEY)
            }
            set({ selectedAdminIndex: 0 })
        }
    }
})
