.stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.stat {
  background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 22px;
  line-height: 26.63px;
  height: 122px;
}

.stat span {
  display: block;
  margin-top: 20px;
}

.aloneStat {
	background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 22px;
  line-height: 26.63px;
  height: 122px;
  width: 350px;
}

.aloneStat span {
  display: block;
  margin-top: 20px;
}

.loaderWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
}

@media screen and (min-width: 640px) {
  .stats {
    flex-direction: row;
    justify-content: flex-end;
  }

  .stat {
    width: 100%;
  }
}
