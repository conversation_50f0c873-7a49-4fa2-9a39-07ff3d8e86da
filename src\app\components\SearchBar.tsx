import React from 'react'
import styles from './styles/SearchBar.module.css'
import { FiSearch } from 'react-icons/fi'
import { SearchBarProps } from '@/types/types'

const SearchBar: React.FC<SearchBarProps> = ({ placeholder = 'Buscar', onSearch, className }) => {
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(event.target.value) // Llama a la función con el texto ingresado
  }

  return (
    <div className={`${styles.searchBar} ${className}`}>
      <FiSearch className={styles.icon} />
      <input
        type="text"
        placeholder={placeholder}
        onChange={handleInputChange}
        className={styles.input}
      />
    </div>
  )
}

export default SearchBar
