'use client'
import { HiRefresh } from "react-icons/hi";
import Header from '../Header'
import Collapse from '../Collapse'
import styles from '../styles/Reporteria.module.css'
import ButtonOutlined from '../ButtonOutlined'
import Select from '../Select'
import DateFilter from '../DateFilter'
import Input from '../Input'

const Content = () => {
  return (
    <div className={styles.container}>
      <Header title="Reportería" />
      <p className={styles.label}>Elige el reporte que deseas descargar:</p>
      <section className={styles.content}>
        <Collapse title="Transacciones Diarias/Mensuales">
          <div className={styles.formContainer}>
            <h5 className={styles.label}>Configurar filtros para descargar tu reporte:</h5>
            <form>
              <ButtonOutlined
                text="Restablecer filtros"
                icon={<HiRefresh />}
                className={styles.resetButton}
                onClick={() => {}}
                type="button"
                disabled={false}
              />
              <div className={styles.formGroupButtons}>
                <label htmlFor="badge" className={styles.badge}>[Tipo transacción] X</label>
                <label htmlFor="badge" className={styles.badge}>Badge filtro X</label>
              </div>
              <div className={styles.formGroup}>
                <Select label="Tipo de transacción" name="typeTransaction">
                  <option value="0">Elige</option>
                </Select>
                <DateFilter onDateChange={() => {}} />
                <Select label="Estado de transacción" name="stateTransaction">
                  <option value="0">Elige estado</option>
                </Select>
                <Input
                  label="Monto de transacción"
                  type="text"
                  name="amountTransaction"
                  placeholder="Escribe monto"
                />
              </div>
              <div className={styles.formGroupButtons}>
                <ButtonOutlined
                  text="Descargar en PDF"
                  onClick={() => {}}
                  type="button"
                  disabled={false}
                />
                <ButtonOutlined
                  text="Descargar en excel"
                  onClick={() => {}}
                  type="button"
                  disabled={false}
                />
              </div>
            </form>
          </div>
        </Collapse>
        <Collapse title="Transacciones Diarias/Mensuales">
          <p className={styles.text}>
            Este reporte muestra las transacciones realizadas en un periodo
            determinado, permitiendo analizar la actividad diaria o mensual.
          </p>
        </Collapse>
        <Collapse title="Transacciones Diarias/Mensuales">
          <p className={styles.text}>
            Este reporte muestra las transacciones realizadas en un periodo
            determinado, permitiendo analizar la actividad diaria o mensual.
          </p>
        </Collapse>
        <Collapse title="Transacciones Diarias/Mensuales">
          <p className={styles.text}>
            Este reporte muestra las transacciones realizadas en un periodo
            determinado, permitiendo analizar la actividad diaria o mensual.
          </p>
        </Collapse>
      </section>
    </div>
  )
}

export default Content
