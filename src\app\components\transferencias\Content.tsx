/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
'use client'
import { useEffect, useState } from 'react'
import ContactList from './components/ContactsList'
import TransferenciaNueva from './components/TransferenciaNueva'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { ROLES } from '@/constants/roles'
import styles from '../styles/Transaction.module.css'
import { TransactionFormData } from '@/hooks/useFormValidationTransaction'
import { Contact } from '@/types/contact/types'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { StepTabs } from '../StepTabs'
import MassiveLoadTransfers from '../modals/massiveLoadModals/MassiveLoadTransfers'
import MassiveLoadLoading from '../modals/massiveLoadModals/MassiveLoadLoading'
import ModalOTPTransferMassive from '../modals/alertModals/ModalOTPTransferMassive'
import { useRouter } from 'next/navigation'
import { BulkTransferData, BulkTransfersStatus } from '@/types/transfers/types'
import { useTransfersStore } from '@/store/transfers/useTransfersStore'
import { generateTransferOTPCode, verifyOTPCode } from '@/api/endpoints/user'
import { toast } from 'react-toastify'
import { canMassTransfer } from '@/permissions/access'
import Button from '../Button'
import { setLastActivity } from '@/utils/token'
import MassiveLoadError from '../modals/massiveLoadModals/MassiveLoadError'

type Props = {
  initialStep?: 'transferencia' | 'contact'
  transferSavedData?: TransactionFormData
}

const STEPS = [
  { key: 'transferencia', label: 'Nueva transferencia' },
  { key: 'contact', label: 'Contactos guardados' },
] as const

export type Step = (typeof STEPS)[number]['key']

const Content = ({ transferSavedData }: Props) => {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState<Step>(STEPS[0]['key'])
  const stepIndex = STEPS.findIndex(step => step.key === currentStep)
  const [formData, setFormData] = useState<TransactionFormData | undefined>(transferSavedData)

  const { getUserRoleName, user } = useAuthStore()
  const isCardManagerClient = getUserRoleName() === ROLES.CLIENTE_TARJETAHABIENTES
  const isConveniaAdmin = getUserRoleName() === ROLES.ADMIN_CONVENIA
  const amountTransfer = useAdminStore(state => state.amountTransfer)
  const fetchAmountTransfer = useAdminStore(state => state.fetchAmountTransfer)

  const [openMassiveLoad, setOpenMassiveLoad] = useState(false)
  const [openModalLoading, setOpenModalLoading] = useState(false)
  const [openOTPModal, setOpenOTPModal] = useState(false)
  const [modalTransferenciaFailed, setModalTransferenciaFailed] = useState(false)
  const [fileId, setFileId] = useState<string | null>(null)
  const [parsedData, setParsedData] = useState<BulkTransferData[] | null>(null)
  const [tokenState, setTokenState] = useState<string | null>(null)
  const [isProcessingMassiveTransfer, setIsProcessingMassiveTransfer] = useState(false)

  const handleSelectContact = (contact: Contact) => {
    setFormData({
      empresa: '',
      numeroCuenta: '',
      cuentaDestino: contact.num_clabe.toString(),
      tipoTransferencia: '',
      banco: contact.bank_institution,
      numeroReferencia: '',
      importe: '',
      nombreBeneficiario: contact.name,
      rfcBeneficiario: contact.rfc || '',
      correo: contact.email || '',
      conceptoBeneficiario: '',
      tipoPago: '',
      guardarDestinatario: false,
    })
    setCurrentStep('transferencia')
  }

  const handleStepChange = (index: number) => {
    setFormData(undefined) // limpiar al cambiar de tab
    setCurrentStep(STEPS[index].key)
  }

  const { createBulkTransfer, checkBulkTransferStatus, fetchBulkTransferErrors, resetBulkState, bulkTransferErrors } =
    useTransfersStore()

  const sendOTPCode = async (email: string) => {
    try {
      const response = await generateTransferOTPCode(email)
      setTokenState(response.data.token)
    } catch (error) {
      console.error('Error sending OTP code:', error)
      toast.error('Error al enviar el código OTP', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    }
  }

  const handleReadyForAuthorization = async (id: string, data: BulkTransferData[]) => {
    setFileId(id)
    setParsedData(data)

    // Activar protección de sesión desde que inicia el proceso de transferencia masiva
    setIsProcessingMassiveTransfer(true)

    await sendOTPCode(user?.email!)
    // setOpenMassiveLoad(false)
    setOpenOTPModal(true)
  }

  const handleOtpContinue = async (otpCode: string) => {
    try {
      const res = await verifyOTPCode({
        email: user?.email!,
        code: otpCode,
        access: tokenState ?? '',
      })

      if (res.statusCode !== 200) {
        toast.error('Código OTP inválido. Intenta nuevamente.', { position: 'top-right' })
        return
      }

      await executeBulkTransfer()
    } catch (err) {
      console.error(err)
      toast.error('Error al verificar el código OTP', { position: 'top-right' })
    }
  }

  const executeBulkTransfer = async () => {
    if (!fileId || !parsedData) return
    try {
      setOpenOTPModal(false)
      setOpenModalLoading(true)

      // Limpiar errores de transferencias anteriores
      resetBulkState()

      const result = await createBulkTransfer(fileId, parsedData)

      // Función para esperar 20 segundos
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

      while (true) {
        // Mantener la sesión activa durante el procesamiento
        setLastActivity()

        const status = await checkBulkTransferStatus(result.id)

        if (status.status === BulkTransfersStatus.COMPLETED) {
          if (status.failureCount > 0) {
            setOpenModalLoading(false)
            setIsProcessingMassiveTransfer(false)

            // Usar result.id (ID original) en lugar de status.id
            await fetchBulkTransferErrors(result.id)
            setModalTransferenciaFailed(true)
          } else {
            setIsProcessingMassiveTransfer(false)
            router.replace('/transferencias/transferencia-masiva-exitosa')
          }
          return
        }

        if (status.status === BulkTransfersStatus.FAILED) {
          setOpenModalLoading(false)
          setIsProcessingMassiveTransfer(false)
          setModalTransferenciaFailed(true)
          return
        }

        // Esperar 20 segundos antes de la siguiente verificación
        await delay(20000) // 20 segundos = 20,000 milisegundos
      }
    } catch (e) {
      console.error(e)
      setOpenModalLoading(false)
      setIsProcessingMassiveTransfer(false)
      setModalTransferenciaFailed(true)
    }
  }

  useEffect(() => {
    if (isConveniaAdmin) {
      fetchAmountTransfer()
    }
  }, [isConveniaAdmin, fetchAmountTransfer])

  // Mantener la sesión activa durante transferencias masivas
  useEffect(() => {
    let keepAliveInterval: NodeJS.Timeout | null = null

    if (isProcessingMassiveTransfer) {
      // Actualizar la actividad cada 30 segundos para mantener la sesión viva
      keepAliveInterval = setInterval(() => {
        setLastActivity()
      }, 30000) // 30 segundos
    }

    return () => {
      if (keepAliveInterval) {
        clearInterval(keepAliveInterval)
      }
    }
  }, [isProcessingMassiveTransfer])

  if (isCardManagerClient) {
    return (
      <div>
        <h1 className={styles.title}>Transferencias</h1>
        <TransferenciaNueva />
      </div>
    )
  }
  const handleTransferComplete = () => {
    setFormData(undefined)
  }

  return (
    <div>
      <StepTabs
        steps={STEPS.map(step => step.label)}
        stepTitles="Transferencias"
        activeStep={stepIndex}
        onStepChange={handleStepChange}
        isOverrideStep
        topButton={
          <>
            {canMassTransfer(getUserRoleName()) && (
              <Button
                text="Transferencia masiva"
                className={styles.buttonMassive}
                onClick={() => setOpenMassiveLoad(true)}
              />
            )}
          </>
        }
      >
        <StepTabs.Panel index={0}>
          <TransferenciaNueva
            transferSavedData={formData}
            showSwitch={!formData}
            onCompleteTransfer={handleTransferComplete}
            isConveniaAdmin={isConveniaAdmin}
            amountTransfer={amountTransfer}
          />
        </StepTabs.Panel>
        <StepTabs.Panel index={1}>
          <ContactList onSelectContact={handleSelectContact} />
        </StepTabs.Panel>
      </StepTabs>
      <MassiveLoadTransfers
        open={openMassiveLoad}
        onClose={() => setOpenMassiveLoad(false)}
        onReadyForAuthorization={handleReadyForAuthorization}
      />
      <MassiveLoadLoading open={openModalLoading} title="Cargando transferencias masivas" />
      <ModalOTPTransferMassive
        open={openOTPModal}
        error={false}
        loading={false}
        onContinue={handleOtpContinue}
        onResendCode={() => sendOTPCode(user?.email!)}
        onClose={() => {
          setOpenOTPModal(false)
          // Desactivar protección de sesión si se cancela el proceso
          setIsProcessingMassiveTransfer(false)
        }}
      />
      <MassiveLoadError
        open={modalTransferenciaFailed}
        errors={bulkTransferErrors?.map(error => {
          // Si el error tiene información de cuenta válida
          if (error.beneficiatyAccount && error.beneficiatyAccount !== 'undefined') {
            return `${error.beneficiatyAccount}: ${error.errorMessage}`
          }

          // Si no hay cuenta o es undefined, mostrar solo el mensaje de error mejorado
          let message = error.errorMessage || 'Error desconocido'

          // Mejorar mensajes específicos
          if (message.includes('Beneficiario no válido')) {
            message = 'Beneficiario no válido. Por favor verifica el nombre del beneficiario.'
          } else if (message.includes('beneficiario')) {
            message = 'Error en los datos del beneficiario. Por favor verifica la información.'
          } else if (message.includes('cuenta')) {
            message = 'Error en la cuenta destino. Por favor verifica que sea una CLABE válida de 18 dígitos.'
          } else if (message.includes('importe') || message.includes('amount')) {
            message = 'Error en el importe. Por favor verifica que sea un número positivo.'
          } else if (message.includes('concepto')) {
            message = 'Error en el concepto. Este campo es obligatorio.'
          }

          return message
        }) || []}
        onClose={() => {
          setModalTransferenciaFailed(false)
          // Desactivar protección de sesión cuando se cierra el modal de errores
          setIsProcessingMassiveTransfer(false)
        }}
      />
    </div>
  )
}

export default Content
