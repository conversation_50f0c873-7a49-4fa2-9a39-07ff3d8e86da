'use client'
import styles from '../../styles/EditClientAdmin.module.css'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { FaArrowLeft } from 'react-icons/fa'
import Button from '@/app/components/Button'
import InformationForm from '../components/InformationForm'
import FileUpload from '../components/FileUpload'
import ContactForm from '../components/ContactForm'
import { FileInput, ClientAdmin } from '@/types/types'
import useFormValidation, { FormData as ClientFormData } from '@/hooks/useFormValidationClient'
import UploadDocumentSuccessModal from '../../modals/alertModals/UploadDocumentSuccessModal'
import CommissionForm from '../components/CommissionForm'
import MultiCompanySelector from '../components/MultiCompanySelector'
// import ModalRegisteredRFC from '../../modals/alertModals/ModalRegisteredRFC'
import { useAdminStore } from '@/store/admin/useAdminStore'
import LoaderFull from '../../loader/LoaderFull'

const typeToKeyMap = {
  ARTICLES_OF_INCORPORATION: 'act',
  CONSTANCY: 'constancy',
  OTHER: 'file',
}

const EditClientAdmin: React.FC<ClientAdmin> = ({ onOpenModal, clientData, isEditing }) => {
  const router = useRouter()
  const [openSuccessModal, setOpenSuccessModal] = useState(false)
  // const [openRFCModal, setOpenRFCModal] = useState(false) // Estado del modal RFC
  const [savingInfoLoading, setSavingInfoLoading] = useState(false)
  const { updateAdmin, adminSelected } = useAdminStore()

  const [currentStep, setCurrentStep] = useState<'information' | 'contact' | 'commission'>(
    'information'
  ) // Maneja la vista activa

  const [formatFiles, setFormatFiles] = useState<
    { key: keyof ClientFormData['files']; file: string; type: string }[]
  >([])

  const filesForForm: ClientFormData['files'] = {
    act: null,
    constancy: null,
    file: null,
    other: null,
  }

  if (Array.isArray(clientData?.files)) {
    clientData.files.forEach(file => {
      const key = typeToKeyMap[
        file.type as keyof typeof typeToKeyMap
      ] as keyof ClientFormData['files']
      if (key) {
        filesForForm[key] = {
          id: file.id,
          type: file.type,
          url: file.url,
        }
      }
    })
  }

  const { formData, errors, validate, setFormData, handleInputChange, handleFileChange } =
    useFormValidation(
      {
        companyName: clientData?.companyName || '',
        alias: clientData?.alias || '',
        rfc: clientData?.rfc || '',
        responsibleName: clientData?.manager?.name || '',
        state: clientData?.address?.state || '',
        city: clientData?.address?.city || '',
        registrationDate: clientData?.createdAt || '',
        assignedCards: clientData?.numAsignedCards?.toString() || '',
        phone: clientData?.manager?.phone?.toString() || '',
        email: clientData?.manager?.email || '',
        street: clientData?.address?.street || '',
        zipCode: clientData?.address?.zipCode || '',
        exteriorNumber: clientData?.address?.numExt?.toString() || '',
        neighborhood: clientData?.address?.colonia || '',
        passwordClient: '',
        confirmPassword: '',
        files: filesForForm,
        commissionPercentage: clientData?.speiIn?.toString() || '',
        commissionAmount: clientData?.speiOut?.toString() || '',
        commissionCardFunding: clientData?.targetRefound?.toString() || '',
        commissionAmbassador: clientData?.ambassador || '',
        extraCompanies: clientData?.manager?.enterprises?.length
          ? clientData.manager.enterprises.map(e => e.id)
          : [],
      },
      isEditing,
      adminSelected?.isSucursal || false
    )

  const fileInputs: FileInput[] = [
    { key: 'act', label: 'Cargar acta constitutiva', shortLabel: 'Acta constitutiva' },
    { key: 'constancy', label: 'Cargar constancia', shortLabel: 'Constancia' },
    { key: 'file', label: 'Cargar archivo', shortLabel: 'Archivo' },
    { key: 'other', label: 'Cargar archivo', shortLabel: 'Archivo' },
  ]

  const handleBack = () => {
    if (currentStep === 'commission') {
      setCurrentStep('contact')
    } else if (currentStep === 'contact') {
      setCurrentStep('information')
    } else {
      router.back() // Regresa a la página anterior
    }
  }

  const informationFields: (keyof ClientFormData)[] = [
    'companyName',
    'alias',
    'rfc',
    'registrationDate',
    'responsibleName',
    'assignedCards',
    'state',
    'city',
  ]

  const contactFields: (keyof ClientFormData)[] = [
    'phone',
    'email',
    'street',
    'zipCode',
    'neighborhood',
    'passwordClient',
    'confirmPassword',
  ]

  const commissionFields: (keyof ClientFormData)[] = [
    'commissionPercentage',
    'commissionAmount',
    'commissionCardFunding',
    'commissionAmbassador',
  ]

  const handleNext = async () => {
    if (currentStep === 'information') {
      if (!validate([...informationFields, 'files'])) return
      // Verificar si el RFC ya está registrado
      // const existingClient = clientData?.rfc === formData.rfc
      // if (existingClient) {
      //   setOpenRFCModal(true)
      //   // return; // Evita avanzar hasta que el usuario confirme en el modal
      // }
      setCurrentStep('contact')
    } else if (currentStep === 'contact') {
      if (!validate([...contactFields, 'extraCompanies'], true)) return
      setCurrentStep('commission')
    } else {
      if (!validate([...commissionFields])) return
      if (!clientData?.id) return
      setSavingInfoLoading(true)
      let filesPayload
      if (formatFiles) {
        filesPayload = formatFiles.map(({ file, type }) => ({ file, type }))
      }
      await updateAdmin(clientData.id, {
        company_name: formData.companyName,
        alias: formData.alias,
        rfc: formData.rfc,
        // is_subaccount: false,
        num_asigned_cards: parseInt(formData.assignedCards),
        address: {
          state: formData.state,
          city: formData.city,
          street: formData.street,
          zip_code: formData.zipCode,
          num_ext: formData.exteriorNumber,
          colonia: formData.neighborhood,
        },
        user: {
          name: formData.responsibleName,
          phone: parseInt(formData.phone),
          email: formData.email,
          password: null,
        },
        spei_in: parseFloat(formData.commissionPercentage || '0'),
        spei_out: parseFloat(formData.commissionAmount || '0'),
        target_refound: parseFloat(formData.commissionCardFunding || '0'),
        ambassador: formData.commissionAmbassador,
        enterprises: formData.extraCompanies?.map(company => ({
          adminId: company,
        })),
        files: filesPayload,
        // enterprises: [],
        // enterprises: [
        //   {
        //     "adminId": "********-d610-4b6d-a7e8-1e80c4055c41"
        //   }
        // ],
      }) // Actualizar el administrador con los nuevos dat
      setSavingInfoLoading(false)
      onOpenModal()
    }
  }

  const isFormIncomplete = (() => {
    if (currentStep === 'information') {
      return informationFields.some(
        field => typeof formData[field] === 'string' && formData[field].trim() === ''
      )
    }
    if (currentStep === 'contact') {
      return contactFields.some(field => {
        const value = formData[field]
        const isPasswordField = field === 'passwordClient' || field === 'confirmPassword'

        return (
          typeof value === 'string' && value.trim() === '' && !(isEditing && isPasswordField) // ← permitir vacío si es edición
        )
      })
    }
    if (currentStep === 'commission') {
      return commissionFields.some(
        field => typeof formData[field] === 'string' && formData[field].trim() === ''
      )
    }
    return false
  })()

  const multiCompanyEnabled =
    formData.extraCompanies !== undefined && formData.extraCompanies.length > 0

  return (
    <div className={styles.container}>
      {/* Botón Atrás */}
      <button className={styles.backButton} onClick={handleBack}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>

      {/* Títulos dinámicos */}
      <p className={styles.text}>
        {currentStep === 'information'
          ? 'Por favor proporciona los siguientes datos'
          : currentStep === 'contact'
            ? 'Por favor completa la información de contacto'
            : 'Por favor proporciona la información de comisiones'}
      </p>
      <div className={styles.tabContainer}>
        <div className={styles.tabContent}>
          <button className={currentStep === 'information' ? styles.activeTab : styles.tab}>
            Información
          </button>
          <button className={currentStep === 'contact' ? styles.activeTab : styles.tab}>
            Contacto
          </button>
          <button className={currentStep === 'commission' ? styles.activeTab : styles.tab}>
            Comisión
          </button>
        </div>
      </div>

      {/* Formularios dinámicos */}
      {currentStep === 'information' && (
        <>
          <InformationForm formData={formData} onChange={handleInputChange} errors={errors} />
          <p className={styles.text}>Agregar documentación del cliente</p>
          <FileUpload
            fileInputs={fileInputs}
            files={formData.files}
            setFiles={handleFileChange}
            errors={errors}
            onSuccess={() => setOpenSuccessModal(true)}
            setFormatFiles={setFormatFiles}
            adminId={clientData?.id}
          />
        </>
      )}

      {currentStep === 'contact' && (
        <>
          <ContactForm
            formData={formData}
            onChange={handleInputChange}
            errors={errors}
            isEditing={isEditing}
          />
          <MultiCompanySelector
            value={formData.extraCompanies || []}
            enabled={multiCompanyEnabled}
            onChange={companies => setFormData(prev => ({ ...prev, extraCompanies: companies }))}
            onToggle={enabled =>
              setFormData(prev => ({
                ...prev,
                extraCompanies: enabled
                  ? prev.extraCompanies.length > 0
                    ? prev.extraCompanies
                    : [''] // conserva si ya tiene
                  : [],
              }))
            }
            errors={errors}
          />
        </>
      )}

      {currentStep === 'commission' && (
        <CommissionForm formData={formData} onChange={handleInputChange} errors={errors} />
      )}

      {/* Botón Siguiente o Guardar */}
      <div className={styles.buttonContainer}>
        <Button
          text={currentStep === 'commission' ? 'Guardar' : 'Siguiente'}
          onClick={handleNext}
          fullWidth
          disabled={isFormIncomplete || savingInfoLoading}
        />
      </div>
      {/* <ModalRegisteredRFC
        open={openRFCModal}
        onClose={() => setOpenRFCModal(false)}
        companyName={formData.companyName}
      /> */}
      <UploadDocumentSuccessModal
        open={openSuccessModal}
        onClose={() => setOpenSuccessModal(false)}
      />
      {savingInfoLoading && <LoaderFull />}
    </div>
  )
}

export default EditClientAdmin
